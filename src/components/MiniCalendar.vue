<template>
  <q-card class="mini-calendar">
    <q-card-section class="q-pa-sm">
      <!-- Header with month navigation -->
      <div class="row items-center justify-between q-mb-sm">
        <q-btn
          flat
          round
          size="sm"
          icon="chevron_left"
          @click="previousMonth"
        />
        
        <div class="text-subtitle2 text-center">
          {{ currentMonthYear }}
        </div>
        
        <q-btn
          flat
          round
          size="sm"
          icon="chevron_right"
          @click="nextMonth"
        />
      </div>

      <!-- Days of week header -->
      <div class="row q-mb-xs">
        <div
          v-for="day in daysOfWeek"
          :key="day"
          class="col text-center text-caption text-grey-6"
        >
          {{ day }}
        </div>
      </div>

      <!-- Calendar grid -->
      <div class="calendar-grid">
        <div
          v-for="(week, weekIndex) in calendarWeeks"
          :key="weekIndex"
          class="row"
        >
          <div
            v-for="(day, dayIndex) in week"
            :key="dayIndex"
            class="col calendar-day"
            :class="getDayClasses(day)"
            @click="selectDate(day.date)"
          >
            <div class="day-content">
              <span class="day-number">{{ day.number }}</span>
              <div v-if="day.hasEvents" class="event-indicator">
                <q-icon name="circle" size="4px" color="primary" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Today button -->
      <div class="row justify-center q-mt-sm">
        <q-btn
          flat
          size="sm"
          label="Hoje"
          @click="goToToday"
        />
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { Event } from 'src/models/event';

interface Props {
  modelValue: Date;
  events: Event[];
}

interface Emits {
  (e: 'update:modelValue', value: Date): void;
  (e: 'date-selected', date: Date): void;
}

interface CalendarDay {
  date: Date;
  number: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  hasEvents: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// State
const currentDate = ref(new Date(props.modelValue));

// Constants
const daysOfWeek = ['D', 'S', 'T', 'Q', 'Q', 'S', 'S'];

// Computed
const currentMonthYear = computed(() => {
  return currentDate.value.toLocaleDateString('pt-BR', {
    month: 'long',
    year: 'numeric'
  });
});

const calendarWeeks = computed(() => {
  const year = currentDate.value.getFullYear();
  const month = currentDate.value.getMonth();
  
  // First day of the month
  const firstDay = new Date(year, month, 1);
  // Last day of the month
  const lastDay = new Date(year, month + 1, 0);
  
  // Start from the beginning of the week containing the first day
  const startDate = new Date(firstDay);
  startDate.setDate(startDate.getDate() - startDate.getDay());
  
  // End at the end of the week containing the last day
  const endDate = new Date(lastDay);
  endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()));
  
  const weeks: CalendarDay[][] = [];
  const currentWeek: CalendarDay[] = [];
  
  const iterDate = new Date(startDate);
  
  while (iterDate <= endDate) {
    const day: CalendarDay = {
      date: new Date(iterDate),
      number: iterDate.getDate(),
      isCurrentMonth: iterDate.getMonth() === month,
      isToday: isToday(iterDate),
      isSelected: isSameDay(iterDate, props.modelValue),
      hasEvents: hasEventsOnDate(iterDate),
    };
    
    currentWeek.push(day);
    
    if (currentWeek.length === 7) {
      weeks.push([...currentWeek]);
      currentWeek.length = 0;
    }
    
    iterDate.setDate(iterDate.getDate() + 1);
  }
  
  return weeks;
});

// Methods
const isToday = (date: Date): boolean => {
  const today = new Date();
  return isSameDay(date, today);
};

const isSameDay = (date1: Date, date2: Date): boolean => {
  return date1.toDateString() === date2.toDateString();
};

const hasEventsOnDate = (date: Date): boolean => {
  return props.events.some(event => {
    const eventDate = new Date(event.startDate);
    return isSameDay(eventDate, date);
  });
};

const getDayClasses = (day: CalendarDay): string[] => {
  const classes: string[] = [];
  
  if (!day.isCurrentMonth) {
    classes.push('other-month');
  }
  
  if (day.isToday) {
    classes.push('today');
  }
  
  if (day.isSelected) {
    classes.push('selected');
  }
  
  if (day.hasEvents) {
    classes.push('has-events');
  }
  
  return classes;
};

const selectDate = (date: Date) => {
  emit('update:modelValue', date);
  emit('date-selected', date);
};

const previousMonth = () => {
  const newDate = new Date(currentDate.value);
  newDate.setMonth(newDate.getMonth() - 1);
  currentDate.value = newDate;
};

const nextMonth = () => {
  const newDate = new Date(currentDate.value);
  newDate.setMonth(newDate.getMonth() + 1);
  currentDate.value = newDate;
};

const goToToday = () => {
  const today = new Date();
  currentDate.value = today;
  selectDate(today);
};

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  currentDate.value = new Date(newValue);
});
</script>

<style lang="scss" scoped>
.mini-calendar {
  width: 280px;
  
  .calendar-grid {
    .calendar-day {
      height: 32px;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.2s;
      
      &:hover {
        background-color: rgba(25, 118, 210, 0.1);
      }
      
      &.other-month {
        opacity: 0.4;
      }
      
      &.today {
        background-color: rgba(25, 118, 210, 0.2);
        font-weight: bold;
      }
      
      &.selected {
        background-color: var(--q-primary);
        color: white;
        
        .event-indicator .q-icon {
          color: white !important;
        }
      }
      
      .day-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        position: relative;
        
        .day-number {
          font-size: 12px;
          line-height: 1;
        }
        
        .event-indicator {
          position: absolute;
          bottom: 2px;
          right: 2px;
        }
      }
    }
  }
}

@media (max-width: 600px) {
  .mini-calendar {
    width: 100%;
    max-width: 320px;
  }
}
</style>
