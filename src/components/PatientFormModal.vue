<template>
  <q-dialog v-model="showModal" persistent>
    <q-card style="min-width: 800px; max-width: 90vw">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">
          {{ isEditing ? $t('patients.edit') : $t('patients.create') }}
        </div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section class="q-pt-none">
        <q-form @submit="onSubmit" class="q-gutter-md">
          <!-- Informações Pessoais -->
          <div class="text-subtitle1 q-mt-md q-mb-sm">Informações Pessoais</div>

          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-input
                v-model="form.name"
                :label="$t('patients.name')"
                outlined
                dense
                :rules="[(val) => !!val || 'Nome é obrigatório']"
                lazy-rules
              />
            </div>

            <div class="col-12 col-md-6">
              <q-input
                v-model="form.lastName"
                :label="$t('patients.lastName')"
                outlined
                dense
                :rules="[(val) => !!val || 'Sobrenome é obrigatório']"
                lazy-rules
              />
            </div>

            <div class="col-12 col-md-6">
              <q-input
                v-model="form.email"
                :label="$t('patients.email')"
                type="email"
                outlined
                dense
              />
            </div>

            <div class="col-12 col-md-6">
              <q-input
                v-model="form.birthDate"
                :label="$t('patients.birthDate')"
                type="date"
                outlined
                dense
                :rules="[(val) => !!val || 'Data de nascimento é obrigatória']"
                lazy-rules
              />
            </div>

            <div class="col-12 col-md-6">
              <q-input v-model="form.cpf" label="CPF" outlined dense mask="###.###.###-##" />
            </div>

            <div class="col-12 col-md-6">
              <q-input v-model="form.rg" label="RG" outlined dense />
            </div>

            <div class="col-12 col-md-6">
              <q-select
                v-model="form.gender"
                :options="genderOptions"
                :label="$t('patients.gender')"
                outlined
                dense
                emit-value
                map-options
                :rules="[(val) => !!val || 'Gênero é obrigatório']"
                lazy-rules
              />
            </div>
          </div>

          <!-- Contato -->
          <div class="text-subtitle1 q-mt-lg q-mb-sm">Contato</div>

          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-input
                v-model="form.phone"
                :label="$t('patients.phone')"
                outlined
                dense
                mask="(##) #####-####"
                :rules="[(val) => !!val || 'Telefone é obrigatório']"
                lazy-rules
              />
            </div>

            <div class="col-12 col-md-6">
              <q-input
                v-model="form.whatsapp"
                :label="$t('patients.whatsapp')"
                outlined
                dense
                mask="(##) #####-####"
              />
            </div>
          </div>

          <!-- Endereço -->
          <div class="text-subtitle1 q-mt-lg q-mb-sm">Endereço</div>

          <div class="row q-gutter-md">
            <div class="col-12 col-md-8">
              <q-input
                v-model="form.address"
                :label="$t('patients.address')"
                outlined
                dense
                :rules="[(val) => !!val || 'Endereço é obrigatório']"
                lazy-rules
              />
            </div>

            <div class="col-12 col-md-4">
              <q-input
                v-model="form.zipCode"
                :label="$t('patients.zipCode')"
                outlined
                dense
                mask="#####-###"
                :rules="[(val) => !!val || 'CEP é obrigatório']"
                lazy-rules
              />
            </div>

            <div class="col-12 col-md-8">
              <q-input
                v-model="form.city"
                :label="$t('patients.city')"
                outlined
                dense
                :rules="[(val) => !!val || 'Cidade é obrigatória']"
                lazy-rules
              />
            </div>

            <div class="col-12 col-md-4">
              <q-select
                v-model="form.state"
                :options="stateOptions"
                :label="$t('patients.state')"
                outlined
                dense
                emit-value
                map-options
                :rules="[(val) => !!val || 'Estado é obrigatório']"
                lazy-rules
              />
            </div>
          </div>
        </q-form>
      </q-card-section>

      <q-card-actions align="right" class="q-pa-md">
        <q-btn flat :label="$t('patients.cancel')" color="grey" v-close-popup />
        <q-btn
          :label="$t('patients.save')"
          color="primary"
          :loading="patientStore.loading"
          @click="onSubmit"
          unelevated
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import type { PatientFormData, Patient } from 'src/models/patient';
import { Gender, BRAZILIAN_STATES, GENDER_OPTIONS } from 'src/models/patient';
import { usePatientStore } from 'src/stores/patient-store';

interface Props {
  modelValue: boolean;
  patient: Patient | undefined;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'saved', patient: Patient): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const $q = useQuasar();
const { t } = useI18n();
const patientStore = usePatientStore();

const showModal = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const isEditing = computed(() => !!props.patient?.id);

const form = ref<PatientFormData>({
  name: '',
  lastName: '',
  email: '',
  phone: '',
  whatsapp: '',
  birthDate: '',
  address: '',
  city: '',
  state: '',
  zipCode: '',
  cpf: '',
  rg: '',
  gender: Gender.NOT_INFORMED,
});

const genderOptions = computed(() =>
  GENDER_OPTIONS.map((option) => ({
    label: t(`patients.genders.${option.value}`),
    value: option.value,
  })),
);

const stateOptions = computed(() =>
  BRAZILIAN_STATES.map((state) => ({
    label: `${state.value} - ${state.label}`,
    value: state.value,
  })),
);

const resetForm = () => {
  form.value = {
    name: '',
    lastName: '',
    email: '',
    phone: '',
    whatsapp: '',
    birthDate: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    cpf: '',
    rg: '',
    gender: Gender.NOT_INFORMED,
  };
};

const loadPatientData = () => {
  if (props.patient) {
    form.value = {
      name: props.patient.name,
      lastName: props.patient.lastName,
      email: props.patient.email || '',
      phone: props.patient.phone,
      whatsapp: props.patient.whatsapp || '',
      birthDate: props.patient.birthDate,
      address: props.patient.address,
      city: props.patient.city,
      state: props.patient.state,
      zipCode: props.patient.zipCode,
      cpf: props.patient.cpf || '',
      rg: props.patient.rg || '',
      gender: props.patient.gender,
    };
  } else {
    resetForm();
  }
};

const onSubmit = async () => {
  try {
    let savedPatient: Patient;

    if (isEditing.value && props.patient?.id) {
      savedPatient = await patientStore.updatePatient(props.patient.id, form.value);
      $q.notify({
        type: 'positive',
        message: t('patients.patientUpdated'),
        position: 'top',
      });
    } else {
      savedPatient = await patientStore.createPatient(form.value);
      $q.notify({
        type: 'positive',
        message: t('patients.patientCreated'),
        position: 'top',
      });
    }

    emit('saved', savedPatient);
    showModal.value = false;
  } catch {
    $q.notify({
      type: 'negative',
      message: t('failed'),
      position: 'top',
    });
  }
};

// Watch for modal opening to load patient data
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      loadPatientData();
    }
  },
);
</script>

<style scoped>
.q-card {
  max-height: 90vh;
  overflow-y: auto;
}
</style>
