<template>
  <q-page class="calendar-page">
    <div class="row q-gutter-md">
      <!-- Mini Calendar Sidebar (Desktop only) -->
      <div class="col-auto gt-sm">
        <MiniCalendar v-model="selectedDate" :events="events" @date-selected="onDateSelected" />
      </div>

      <!-- Main Calendar Content -->
      <div class="col">
        <!-- Header with view controls -->
        <div class="row items-center justify-between q-mb-md">
          <div class="row items-center q-gutter-md">
            <h4 class="q-ma-none">{{ formattedCurrentDate }}</h4>

            <!-- Mobile Mini Calendar Toggle -->
            <q-btn
              class="lt-md"
              flat
              round
              icon="calendar_month"
              @click="showMobileCalendar = true"
            >
              <q-tooltip>Selecionar Data</q-tooltip>
            </q-btn>
          </div>

          <div class="row items-center q-gutter-sm">
            <!-- Create Event Button -->
            <q-btn
              color="primary"
              icon="add"
              :label="$q.screen.gt.xs ? 'Novo Evento' : ''"
              @click="openCreateEventModal"
            >
              <q-tooltip v-if="$q.screen.xs">Novo Evento</q-tooltip>
            </q-btn>

            <q-separator vertical />

            <!-- Navigation buttons -->
            <q-btn flat round icon="chevron_left" @click="navigatePrevious" />

            <q-btn flat label="Hoje" @click="goToToday" />

            <q-btn flat round icon="chevron_right" @click="navigateNext" />

            <q-separator vertical />

            <!-- View toggle buttons -->
            <q-btn-toggle
              v-model="currentView"
              toggle-color="primary"
              :options="viewOptions"
              @update:model-value="onViewChange"
            />
          </div>
        </div>

        <!-- Calendar Views -->
        <div class="calendar-content">
          <!-- Month View -->
          <div v-if="currentView === 'month'" class="month-view">
            <MonthView
              :current-date="selectedDate"
              :events="filteredEvents"
              @date-clicked="onDateClicked"
              @event-clicked="onEventClicked"
            />
          </div>

          <!-- Week View -->
          <div v-if="currentView === 'week'" class="week-view">
            <WeekView
              :current-date="selectedDate"
              :events="filteredEvents"
              @date-clicked="onDateClicked"
              @event-clicked="onEventClicked"
            />
          </div>

          <!-- Day View -->
          <div v-if="currentView === 'day'" class="day-view">
            <DayView
              :current-date="selectedDate"
              :events="filteredEvents"
              @event-clicked="onEventClicked"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Mini Calendar Dialog -->
    <q-dialog v-model="showMobileCalendar" class="lt-md">
      <q-card style="min-width: 300px">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">Selecionar Data</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <MiniCalendar
            v-model="selectedDate"
            :events="events"
            @date-selected="onMobileDateSelected"
          />
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- Event Details Dialog -->
    <q-dialog v-model="showEventDialog">
      <q-card style="min-width: 400px">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">{{ selectedEvent?.title }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section v-if="selectedEvent">
          <div class="q-gutter-sm">
            <div class="row items-center">
              <q-icon name="schedule" class="q-mr-sm" />
              <span>{{ formatEventTime(selectedEvent.startDate, selectedEvent.endDate) }}</span>
            </div>

            <div v-if="selectedEvent.patientName" class="row items-center">
              <q-icon name="person" class="q-mr-sm" />
              <span>{{ selectedEvent.patientName }}</span>
            </div>

            <div v-if="selectedEvent.location" class="row items-center">
              <q-icon name="place" class="q-mr-sm" />
              <span>{{ selectedEvent.location }}</span>
            </div>

            <div class="row items-center">
              <q-icon name="label" class="q-mr-sm" />
              <q-chip
                :color="getEventTypeOption(selectedEvent.type)?.color"
                text-color="white"
                size="sm"
              >
                {{ getEventTypeOption(selectedEvent.type)?.label }}
              </q-chip>
            </div>

            <div class="row items-center">
              <q-icon name="info" class="q-mr-sm" />
              <q-chip
                :color="getEventStatusOption(selectedEvent.status)?.color"
                text-color="white"
                size="sm"
              >
                {{ getEventStatusOption(selectedEvent.status)?.label }}
              </q-chip>
            </div>

            <div v-if="selectedEvent.description" class="q-mt-md">
              <div class="text-subtitle2">Descrição:</div>
              <div>{{ selectedEvent.description }}</div>
            </div>

            <div v-if="selectedEvent.notes" class="q-mt-md">
              <div class="text-subtitle2">Observações:</div>
              <div>{{ selectedEvent.notes }}</div>
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Fechar" color="primary" v-close-popup />
          <q-btn flat label="Editar" color="primary" @click="editEvent" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Event Form Modal -->
    <EventFormModal
      v-model="showEventFormModal"
      :event="selectedEventForEdit"
      :initial-date="selectedDate"
      @saved="onEventSaved"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { useEventStore } from 'src/stores/event-store';
import type { Event } from 'src/models/event';
import {
  CalendarView,
  formatEventTime,
  getEventTypeOption,
  getEventStatusOption,
} from 'src/models/event';
import MiniCalendar from 'src/components/MiniCalendar.vue';
import MonthView from 'src/components/MonthView.vue';
import WeekView from 'src/components/WeekView.vue';
import DayView from 'src/components/DayView.vue';
import EventFormModal from 'src/components/EventFormModal.vue';

const $q = useQuasar();
const eventStore = useEventStore();

// State
const selectedDate = ref(new Date());
const currentView = ref<CalendarView>(CalendarView.MONTH);
const showMobileCalendar = ref(false);
const showEventDialog = ref(false);
const selectedEvent = ref<Event | null>(null);
const showEventFormModal = ref(false);
const selectedEventForEdit = ref<Event | undefined>(undefined);

// View options for toggle
const viewOptions = [
  { label: 'Mês', value: CalendarView.MONTH },
  { label: 'Semana', value: CalendarView.WEEK },
  { label: 'Dia', value: CalendarView.DAY },
];

// Computed
const events = computed(() => eventStore.events);

const formattedCurrentDate = computed(() => {
  const date = selectedDate.value;

  switch (currentView.value) {
    case CalendarView.MONTH:
      return date.toLocaleDateString('pt-BR', {
        month: 'long',
        year: 'numeric',
      });
    case CalendarView.WEEK: {
      const weekStart = getWeekStart(date);
      const weekEnd = getWeekEnd(date);
      return `${weekStart.toLocaleDateString('pt-BR', {
        day: 'numeric',
        month: 'short',
      })} - ${weekEnd.toLocaleDateString('pt-BR', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      })}`;
    }
    case CalendarView.DAY:
      return date.toLocaleDateString('pt-BR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long',
        year: 'numeric',
      });
    default:
      return '';
  }
});

const filteredEvents = computed(() => {
  const date = selectedDate.value;

  switch (currentView.value) {
    case CalendarView.MONTH: {
      const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
      const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);
      return eventStore.getEventsByDateRange(monthStart, monthEnd);
    }
    case CalendarView.WEEK: {
      const weekStart = getWeekStart(date);
      const weekEnd = getWeekEnd(date);
      return eventStore.getEventsByDateRange(weekStart, weekEnd);
    }
    case CalendarView.DAY:
      return eventStore.getEventsByDate(date);

    default:
      return [];
  }
});

// Methods
const getWeekStart = (date: Date): Date => {
  const start = new Date(date);
  const day = start.getDay();
  const diff = start.getDate() - day;
  return new Date(start.setDate(diff));
};

const getWeekEnd = (date: Date): Date => {
  const end = getWeekStart(date);
  end.setDate(end.getDate() + 6);
  return end;
};

const navigatePrevious = () => {
  const newDate = new Date(selectedDate.value);

  switch (currentView.value) {
    case CalendarView.MONTH:
      newDate.setMonth(newDate.getMonth() - 1);
      break;
    case CalendarView.WEEK:
      newDate.setDate(newDate.getDate() - 7);
      break;
    case CalendarView.DAY:
      newDate.setDate(newDate.getDate() - 1);
      break;
  }

  selectedDate.value = newDate;
};

const navigateNext = () => {
  const newDate = new Date(selectedDate.value);

  switch (currentView.value) {
    case CalendarView.MONTH:
      newDate.setMonth(newDate.getMonth() + 1);
      break;
    case CalendarView.WEEK:
      newDate.setDate(newDate.getDate() + 7);
      break;
    case CalendarView.DAY:
      newDate.setDate(newDate.getDate() + 1);
      break;
  }

  selectedDate.value = newDate;
};

const goToToday = () => {
  selectedDate.value = new Date();
};

const onViewChange = (view: CalendarView) => {
  currentView.value = view;
};

const onDateSelected = (date: Date) => {
  selectedDate.value = date;
};

const onMobileDateSelected = (date: Date) => {
  selectedDate.value = date;
  showMobileCalendar.value = false;
};

const onDateClicked = (date: Date) => {
  selectedDate.value = date;
  if (currentView.value !== CalendarView.DAY) {
    currentView.value = CalendarView.DAY;
  }
};

const onEventClicked = (event: Event) => {
  selectedEvent.value = event;
  showEventDialog.value = true;
};

const openCreateEventModal = () => {
  selectedEventForEdit.value = undefined;
  showEventFormModal.value = true;
};

const editEvent = () => {
  selectedEventForEdit.value = selectedEvent.value || undefined;
  showEventDialog.value = false;
  showEventFormModal.value = true;
};

const onEventSaved = (event: Event) => {
  // Refresh events after saving
  void eventStore.loadEvents();
};

// Lifecycle
onMounted(() => {
  void eventStore.loadEvents();
});
</script>

<style lang="scss" scoped>
.calendar-page {
  padding: 16px;
}

.calendar-content {
  min-height: 600px;
}

.month-view,
.week-view,
.day-view {
  width: 100%;
}

@media (max-width: 768px) {
  .calendar-page {
    padding: 8px;
  }

  .calendar-content {
    min-height: 400px;
  }

  // Hide view toggle labels on mobile
  :deep(.q-btn-toggle .q-btn__content) {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .calendar-page {
    padding: 4px;
  }

  // Stack header elements on very small screens
  .row.items-center.justify-between {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;

    .row.items-center.q-gutter-md {
      justify-content: center;
    }

    .row.items-center.q-gutter-sm {
      justify-content: center;
      flex-wrap: wrap;
    }
  }
}
</style>
