<template>
  <q-dialog v-model="showModal" persistent>
    <q-card style="min-width: 600px; max-width: 90vw">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">
          {{ isEditing ? 'Editar Tipo de Evento' : 'Novo Tipo de Evento' }}
        </div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section>
        <q-form @submit="onSubmit" class="q-gutter-md">
          <!-- Nome -->
          <q-input
            v-model="form.name"
            label="Nome *"
            outlined
            :rules="[validateEventTypeName]"
            :disable="isEditingDefault"
          />

          <!-- Descrição -->
          <q-input
            v-model="form.description"
            label="Descrição"
            outlined
            type="textarea"
            rows="3"
            :rules="[validateEventTypeDescription]"
          />

          <!-- Ícone -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Ícone *</div>
            <div class="row q-gutter-sm">
              <q-btn
                v-for="icon in AVAILABLE_ICONS.slice(0, showAllIcons ? AVAILABLE_ICONS.length : 12)"
                :key="icon.value"
                :icon="icon.value"
                :color="form.icon === icon.value ? 'primary' : 'grey-4'"
                :text-color="form.icon === icon.value ? 'white' : 'grey-8'"
                size="md"
                @click="form.icon = icon.value"
              >
                <q-tooltip>{{ icon.label }}</q-tooltip>
              </q-btn>
              
              <q-btn
                v-if="!showAllIcons && AVAILABLE_ICONS.length > 12"
                flat
                icon="more_horiz"
                color="grey-6"
                @click="showAllIcons = true"
              >
                <q-tooltip>Ver mais ícones</q-tooltip>
              </q-btn>
            </div>
            
            <div v-if="showAllIcons && AVAILABLE_ICONS.length > 12" class="q-mt-sm">
              <q-btn
                flat
                icon="expand_less"
                label="Mostrar menos"
                color="grey-6"
                size="sm"
                @click="showAllIcons = false"
              />
            </div>
          </div>

          <!-- Cor -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Cor *</div>
            <div class="row q-gutter-sm">
              <q-btn
                v-for="color in AVAILABLE_COLORS.slice(0, showAllColors ? AVAILABLE_COLORS.length : 12)"
                :key="color.value"
                :style="`background-color: ${color.hex}; border: ${form.color === color.value ? '3px solid #333' : '1px solid #ddd'}`"
                size="md"
                round
                @click="form.color = color.value"
                class="color-btn"
              >
                <q-tooltip>{{ color.label }}</q-tooltip>
              </q-btn>
              
              <q-btn
                v-if="!showAllColors && AVAILABLE_COLORS.length > 12"
                flat
                icon="more_horiz"
                color="grey-6"
                round
                @click="showAllColors = true"
              >
                <q-tooltip>Ver mais cores</q-tooltip>
              </q-btn>
            </div>
            
            <div v-if="showAllColors && AVAILABLE_COLORS.length > 12" class="q-mt-sm">
              <q-btn
                flat
                icon="expand_less"
                label="Mostrar menos"
                color="grey-6"
                size="sm"
                @click="showAllColors = false"
              />
            </div>
          </div>

          <!-- Preview -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Pré-visualização</div>
            <q-card flat bordered class="q-pa-md">
              <div class="row items-center q-gutter-md">
                <q-icon
                  :name="form.icon"
                  :color="form.color"
                  size="lg"
                />
                <div>
                  <div class="text-h6">{{ form.name || 'Nome do Tipo' }}</div>
                  <div class="text-caption text-grey-6">
                    {{ form.description || 'Descrição do tipo de evento' }}
                  </div>
                </div>
                <q-chip
                  :color="form.color"
                  text-color="white"
                  :label="form.name || 'Tipo'"
                />
              </div>
            </q-card>
          </div>

          <!-- Status -->
          <q-toggle
            v-model="form.isActive"
            label="Tipo ativo"
            :disable="isEditingDefault"
          />
        </q-form>
      </q-card-section>

      <q-card-actions align="right" class="q-pa-md">
        <q-btn flat label="Cancelar" color="primary" v-close-popup />
        <q-btn
          label="Salvar"
          color="primary"
          @click="onSubmit"
          :loading="loading"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import type { EventTypeFormData, CustomEventType } from 'src/models/event-type';
import { 
  AVAILABLE_ICONS, 
  AVAILABLE_COLORS,
  validateEventTypeName,
  validateEventTypeDescription
} from 'src/models/event-type';
import { useEventTypeStore } from 'src/stores/event-type-store';

interface Props {
  modelValue: boolean;
  eventType?: CustomEventType;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'saved', eventType: CustomEventType): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const $q = useQuasar();
const eventTypeStore = useEventTypeStore();

// State
const loading = ref(false);
const showAllIcons = ref(false);
const showAllColors = ref(false);

// Form data
const form = ref<EventTypeFormData>({
  name: '',
  description: '',
  icon: 'event',
  color: 'primary',
  isActive: true,
});

// Computed
const showModal = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const isEditing = computed(() => !!props.eventType?.id);

const isEditingDefault = computed(() => {
  return isEditing.value && props.eventType?.isDefault;
});

// Methods
const resetForm = () => {
  form.value = {
    name: '',
    description: '',
    icon: 'event',
    color: 'primary',
    isActive: true,
  };
  showAllIcons.value = false;
  showAllColors.value = false;
};

const loadFormData = () => {
  if (props.eventType) {
    form.value = {
      name: props.eventType.name,
      description: props.eventType.description || '',
      icon: props.eventType.icon,
      color: props.eventType.color,
      isActive: props.eventType.isActive,
    };
  } else {
    resetForm();
  }
};

const onSubmit = async () => {
  loading.value = true;

  try {
    let savedEventType: CustomEventType;

    if (isEditing.value && props.eventType) {
      savedEventType = await eventTypeStore.updateEventType(props.eventType.id, form.value);
    } else {
      savedEventType = await eventTypeStore.createEventType(form.value);
    }

    emit('saved', savedEventType);
    showModal.value = false;
  } catch (error) {
    // Error is already handled in the store and shown via error state
    // The store will set the error message that can be displayed
  } finally {
    loading.value = false;
  }
};

// Watchers
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    loadFormData();
  }
});
</script>

<style lang="scss" scoped>
.color-btn {
  width: 40px;
  height: 40px;
  min-width: 40px;
  border-radius: 50%;
  transition: all 0.2s;
  
  &:hover {
    transform: scale(1.1);
  }
}

@media (max-width: 600px) {
  .q-card {
    min-width: 95vw !important;
  }
}
</style>
