import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { CustomEventType, EventTypeFormData, EventTypeFilters } from 'src/models/event-type';
import { DEFAULT_EVENT_TYPES } from 'src/models/event-type';

export const useEventTypeStore = defineStore('eventType', () => {
  // State
  const eventTypes = ref<CustomEventType[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Initialize with default types
  const initializeDefaultTypes = () => {
    const now = new Date().toISOString();
    const defaultTypes: CustomEventType[] = DEFAULT_EVENT_TYPES.map((type, index) => ({
      ...type,
      id: `default-${index + 1}`,
      createdAt: now,
      updatedAt: now,
    }));
    
    eventTypes.value = [...defaultTypes];
  };

  // Getters
  const activeEventTypes = computed(() => {
    return eventTypes.value.filter(type => type.isActive);
  });

  const customEventTypes = computed(() => {
    return eventTypes.value.filter(type => !type.isDefault);
  });

  const defaultEventTypes = computed(() => {
    return eventTypes.value.filter(type => type.isDefault);
  });

  const totalEventTypes = computed(() => eventTypes.value.length);

  const eventTypeOptions = computed(() => {
    return activeEventTypes.value.map(type => ({
      label: type.name,
      value: type.id,
      icon: type.icon,
      color: type.color,
      description: type.description,
    }));
  });

  // Actions
  const loadEventTypes = async (filters?: EventTypeFilters): Promise<void> => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 300));

      let filteredTypes = [...eventTypes.value];

      if (filters) {
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          filteredTypes = filteredTypes.filter(type =>
            type.name.toLowerCase().includes(searchLower) ||
            type.description?.toLowerCase().includes(searchLower)
          );
        }

        if (filters.isActive !== undefined) {
          filteredTypes = filteredTypes.filter(type => type.isActive === filters.isActive);
        }
      }

      // In a real app, this would update the eventTypes from the filtered API response
      // For now, we just simulate the loading
    } catch (err) {
      error.value = 'Erro ao carregar tipos de eventos';
      console.error('Error loading event types:', err);
    } finally {
      loading.value = false;
    }
  };

  const getEventTypeById = (id: string): CustomEventType | undefined => {
    return eventTypes.value.find(type => type.id === id);
  };

  const createEventType = async (typeData: EventTypeFormData): Promise<CustomEventType> => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Check if name already exists
      const existingType = eventTypes.value.find(
        type => type.name.toLowerCase() === typeData.name.toLowerCase()
      );

      if (existingType) {
        throw new Error('Já existe um tipo de evento com este nome');
      }

      const newEventType: CustomEventType = {
        id: Date.now().toString(),
        ...typeData,
        isDefault: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      eventTypes.value.push(newEventType);

      return newEventType;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Erro ao criar tipo de evento';
      console.error('Error creating event type:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateEventType = async (id: string, typeData: Partial<EventTypeFormData>): Promise<CustomEventType> => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      const typeIndex = eventTypes.value.findIndex(type => type.id === id);
      if (typeIndex === -1) {
        throw new Error('Tipo de evento não encontrado');
      }

      const existingType = eventTypes.value[typeIndex];

      // Check if it's a default type and trying to change critical properties
      if (existingType.isDefault && (typeData.name || typeData.isActive === false)) {
        throw new Error('Tipos de eventos padrão não podem ter o nome alterado ou ser desativados');
      }

      // Check if name already exists (excluding current type)
      if (typeData.name) {
        const duplicateType = eventTypes.value.find(
          type => type.id !== id && type.name.toLowerCase() === typeData.name!.toLowerCase()
        );

        if (duplicateType) {
          throw new Error('Já existe um tipo de evento com este nome');
        }
      }

      const updatedEventType: CustomEventType = {
        ...existingType,
        ...typeData,
        updatedAt: new Date().toISOString(),
      };

      eventTypes.value[typeIndex] = updatedEventType;

      return updatedEventType;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Erro ao atualizar tipo de evento';
      console.error('Error updating event type:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const deleteEventType = async (id: string): Promise<void> => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      const typeIndex = eventTypes.value.findIndex(type => type.id === id);
      if (typeIndex === -1) {
        throw new Error('Tipo de evento não encontrado');
      }

      const eventType = eventTypes.value[typeIndex];

      // Cannot delete default types
      if (eventType.isDefault) {
        throw new Error('Tipos de eventos padrão não podem ser excluídos');
      }

      // TODO: Check if type is being used by any events
      // In a real app, this would be an API call to check dependencies

      eventTypes.value.splice(typeIndex, 1);
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Erro ao excluir tipo de evento';
      console.error('Error deleting event type:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const toggleEventTypeStatus = async (id: string): Promise<CustomEventType> => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 300));

      const typeIndex = eventTypes.value.findIndex(type => type.id === id);
      if (typeIndex === -1) {
        throw new Error('Tipo de evento não encontrado');
      }

      const eventType = eventTypes.value[typeIndex];

      // Cannot deactivate default types
      if (eventType.isDefault && eventType.isActive) {
        throw new Error('Tipos de eventos padrão não podem ser desativados');
      }

      const updatedEventType: CustomEventType = {
        ...eventType,
        isActive: !eventType.isActive,
        updatedAt: new Date().toISOString(),
      };

      eventTypes.value[typeIndex] = updatedEventType;

      return updatedEventType;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Erro ao alterar status do tipo de evento';
      console.error('Error toggling event type status:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const duplicateEventType = async (id: string): Promise<CustomEventType> => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      const originalType = eventTypes.value.find(type => type.id === id);
      if (!originalType) {
        throw new Error('Tipo de evento não encontrado');
      }

      // Generate unique name
      let newName = `${originalType.name} (Cópia)`;
      let counter = 1;
      while (eventTypes.value.some(type => type.name === newName)) {
        counter++;
        newName = `${originalType.name} (Cópia ${counter})`;
      }

      const duplicatedType: CustomEventType = {
        ...originalType,
        id: Date.now().toString(),
        name: newName,
        isDefault: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      eventTypes.value.push(duplicatedType);

      return duplicatedType;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Erro ao duplicar tipo de evento';
      console.error('Error duplicating event type:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // Initialize with default types on store creation
  initializeDefaultTypes();

  return {
    // State
    eventTypes,
    loading,
    error,

    // Getters
    activeEventTypes,
    customEventTypes,
    defaultEventTypes,
    totalEventTypes,
    eventTypeOptions,

    // Actions
    loadEventTypes,
    getEventTypeById,
    createEventType,
    updateEventType,
    deleteEventType,
    toggleEventTypeStatus,
    duplicateEventType,
  };
});
