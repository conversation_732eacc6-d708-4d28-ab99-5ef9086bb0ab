<template>
  <div class="month-view">
    <!-- Days of week header -->
    <div class="row q-mb-sm">
      <div
        v-for="day in daysOfWeek"
        :key="day"
        class="col text-center text-subtitle2 text-grey-7 q-py-sm"
      >
        {{ day }}
      </div>
    </div>

    <!-- Calendar grid -->
    <div class="calendar-grid">
      <div
        v-for="(week, weekIndex) in calendarWeeks"
        :key="weekIndex"
        class="row calendar-week"
      >
        <div
          v-for="(day, dayIndex) in week"
          :key="dayIndex"
          class="col calendar-day"
          :class="getDayClasses(day)"
          @click="onDayClick(day.date)"
        >
          <div class="day-header">
            <span class="day-number">{{ day.number }}</span>
          </div>
          
          <div class="day-events">
            <div
              v-for="event in day.events.slice(0, 3)"
              :key="event.id"
              class="event-item"
              :class="`event-${getEventTypeOption(event.type)?.color}`"
              @click.stop="onEventClick(event)"
            >
              <span class="event-title">
                {{ getShortEventDescription(event, 12) }}
              </span>
            </div>
            
            <div
              v-if="day.events.length > 3"
              class="more-events"
              @click.stop="onDayClick(day.date)"
            >
              +{{ day.events.length - 3 }} mais
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { Event } from 'src/models/event';
import { getEventTypeOption, getShortEventDescription } from 'src/models/event';

interface Props {
  currentDate: Date;
  events: Event[];
}

interface Emits {
  (e: 'date-clicked', date: Date): void;
  (e: 'event-clicked', event: Event): void;
}

interface CalendarDay {
  date: Date;
  number: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  events: Event[];
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Constants
const daysOfWeek = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];

// Computed
const calendarWeeks = computed(() => {
  const year = props.currentDate.getFullYear();
  const month = props.currentDate.getMonth();
  
  // First day of the month
  const firstDay = new Date(year, month, 1);
  // Last day of the month
  const lastDay = new Date(year, month + 1, 0);
  
  // Start from the beginning of the week containing the first day
  const startDate = new Date(firstDay);
  startDate.setDate(startDate.getDate() - startDate.getDay());
  
  // End at the end of the week containing the last day
  const endDate = new Date(lastDay);
  endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()));
  
  const weeks: CalendarDay[][] = [];
  const currentWeek: CalendarDay[] = [];
  
  const iterDate = new Date(startDate);
  
  while (iterDate <= endDate) {
    const day: CalendarDay = {
      date: new Date(iterDate),
      number: iterDate.getDate(),
      isCurrentMonth: iterDate.getMonth() === month,
      isToday: isToday(iterDate),
      events: getEventsForDate(iterDate),
    };
    
    currentWeek.push(day);
    
    if (currentWeek.length === 7) {
      weeks.push([...currentWeek]);
      currentWeek.length = 0;
    }
    
    iterDate.setDate(iterDate.getDate() + 1);
  }
  
  return weeks;
});

// Methods
const isToday = (date: Date): boolean => {
  const today = new Date();
  return date.toDateString() === today.toDateString();
};

const getEventsForDate = (date: Date): Event[] => {
  return props.events.filter(event => {
    const eventDate = new Date(event.startDate);
    return eventDate.toDateString() === date.toDateString();
  });
};

const getDayClasses = (day: CalendarDay): string[] => {
  const classes: string[] = [];
  
  if (!day.isCurrentMonth) {
    classes.push('other-month');
  }
  
  if (day.isToday) {
    classes.push('today');
  }
  
  return classes;
};

const onDayClick = (date: Date) => {
  emit('date-clicked', date);
};

const onEventClick = (event: Event) => {
  emit('event-clicked', event);
};
</script>

<style lang="scss" scoped>
.month-view {
  .calendar-grid {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    
    .calendar-week {
      &:not(:last-child) {
        border-bottom: 1px solid #e0e0e0;
      }
    }
    
    .calendar-day {
      min-height: 120px;
      border-right: 1px solid #e0e0e0;
      padding: 4px;
      cursor: pointer;
      transition: background-color 0.2s;
      
      &:last-child {
        border-right: none;
      }
      
      &:hover {
        background-color: rgba(25, 118, 210, 0.05);
      }
      
      &.other-month {
        background-color: #fafafa;
        opacity: 0.6;
      }
      
      &.today {
        background-color: rgba(25, 118, 210, 0.1);
        
        .day-number {
          background-color: var(--q-primary);
          color: white;
          border-radius: 50%;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
        }
      }
      
      .day-header {
        display: flex;
        justify-content: flex-start;
        margin-bottom: 4px;
        
        .day-number {
          font-size: 14px;
          font-weight: 500;
        }
      }
      
      .day-events {
        .event-item {
          background-color: var(--q-primary);
          color: white;
          padding: 2px 6px;
          margin-bottom: 2px;
          border-radius: 3px;
          font-size: 11px;
          cursor: pointer;
          transition: opacity 0.2s;
          
          &:hover {
            opacity: 0.8;
          }
          
          .event-title {
            display: block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          // Event type colors
          &.event-primary { background-color: var(--q-primary); }
          &.event-secondary { background-color: var(--q-secondary); }
          &.event-accent { background-color: var(--q-accent); }
          &.event-info { background-color: var(--q-info); }
          &.event-warning { background-color: var(--q-warning); }
          &.event-positive { background-color: var(--q-positive); }
          &.event-negative { background-color: var(--q-negative); }
          &.event-grey { background-color: #9e9e9e; }
        }
        
        .more-events {
          font-size: 10px;
          color: #666;
          cursor: pointer;
          padding: 2px 4px;
          
          &:hover {
            color: var(--q-primary);
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .month-view {
    .calendar-grid {
      .calendar-day {
        min-height: 80px;
        
        .day-events {
          .event-item {
            font-size: 10px;
            padding: 1px 4px;
          }
        }
      }
    }
  }
}
</style>
