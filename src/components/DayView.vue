<template>
  <div class="day-view">
    <!-- Day header -->
    <div class="day-header">
      <h5 class="q-ma-none">
        {{ formattedDate }}
      </h5>
      <div class="text-subtitle2 text-grey-6">
        {{ dayEvents.length }} evento{{ dayEvents.length !== 1 ? 's' : '' }}
      </div>
    </div>

    <!-- Time grid -->
    <div class="day-grid">
      <div class="time-column">
        <div
          v-for="hour in hours"
          :key="hour"
          class="time-slot"
        >
          {{ formatHour(hour) }}
        </div>
      </div>

      <div class="events-column">
        <!-- Hour slots -->
        <div
          v-for="hour in hours"
          :key="hour"
          class="hour-slot"
          :class="{ 'current-hour': isCurrentHour(hour) }"
        ></div>

        <!-- Events -->
        <div
          v-for="event in dayEvents"
          :key="event.id"
          class="event-item"
          :class="`event-${getEventTypeOption(event.type)?.color}`"
          :style="getEventStyle(event)"
          @click="onEventClick(event)"
        >
          <div class="event-header">
            <div class="event-time">{{ formatEventTime(event.startDate, event.endDate) }}</div>
            <q-chip
              :color="getEventStatusOption(event.status)?.color"
              text-color="white"
              size="sm"
              class="event-status"
            >
              {{ getEventStatusOption(event.status)?.label }}
            </q-chip>
          </div>
          
          <div class="event-title">{{ event.title }}</div>
          
          <div v-if="event.patientName" class="event-patient">
            <q-icon name="person" size="sm" class="q-mr-xs" />
            {{ event.patientName }}
          </div>
          
          <div v-if="event.location" class="event-location">
            <q-icon name="place" size="sm" class="q-mr-xs" />
            {{ event.location }}
          </div>
          
          <div v-if="event.description" class="event-description">
            {{ event.description }}
          </div>
        </div>

        <!-- Current time indicator -->
        <div
          v-if="isToday"
          class="current-time-indicator"
          :style="currentTimeStyle"
        >
          <div class="time-line"></div>
          <div class="time-dot"></div>
        </div>
      </div>
    </div>

    <!-- Empty state -->
    <div v-if="dayEvents.length === 0" class="empty-state">
      <q-icon name="event_available" size="64px" color="grey-4" />
      <div class="text-h6 text-grey-6 q-mt-md">
        Nenhum evento agendado
      </div>
      <div class="text-body2 text-grey-5">
        Este dia está livre para novos agendamentos
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { Event } from 'src/models/event';
import { getEventTypeOption, getEventStatusOption, formatEventTime } from 'src/models/event';

interface Props {
  currentDate: Date;
  events: Event[];
}

interface Emits {
  (e: 'event-clicked', event: Event): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Constants
const hours = Array.from({ length: 24 }, (_, i) => i);

// Computed
const formattedDate = computed(() => {
  return props.currentDate.toLocaleDateString('pt-BR', {
    weekday: 'long',
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });
});

const dayEvents = computed(() => {
  return props.events
    .filter(event => {
      const eventDate = new Date(event.startDate);
      return eventDate.toDateString() === props.currentDate.toDateString();
    })
    .sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime());
});

const isToday = computed(() => {
  const today = new Date();
  return props.currentDate.toDateString() === today.toDateString();
});

const currentTimeStyle = computed(() => {
  if (!isToday.value) return {};
  
  const now = new Date();
  const hour = now.getHours();
  const minute = now.getMinutes();
  const position = (hour + minute / 60) * 80; // 80px per hour
  
  return {
    top: `${position}px`,
  };
});

// Methods
const formatHour = (hour: number): string => {
  return `${hour.toString().padStart(2, '0')}:00`;
};

const isCurrentHour = (hour: number): boolean => {
  if (!isToday.value) return false;
  const now = new Date();
  return now.getHours() === hour;
};

const getEventStyle = (event: Event): Record<string, string> => {
  const startDate = new Date(event.startDate);
  const endDate = new Date(event.endDate);
  
  const startHour = startDate.getHours();
  const startMinute = startDate.getMinutes();
  const endHour = endDate.getHours();
  const endMinute = endDate.getMinutes();
  
  const startPosition = (startHour + startMinute / 60) * 80; // 80px per hour
  const duration = ((endHour + endMinute / 60) - (startHour + startMinute / 60)) * 80;
  
  return {
    top: `${startPosition}px`,
    height: `${Math.max(duration, 60)}px`, // Minimum 60px height
    left: '8px',
    right: '8px',
  };
};

const onEventClick = (event: Event) => {
  emit('event-clicked', event);
};
</script>

<style lang="scss" scoped>
.day-view {
  .day-header {
    padding: 16px 0;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 16px;
  }
  
  .day-grid {
    display: flex;
    position: relative;
    min-height: 600px;
    
    .time-column {
      width: 80px;
      flex-shrink: 0;
      border-right: 1px solid #e0e0e0;
      
      .time-slot {
        height: 80px;
        padding: 8px;
        font-size: 14px;
        color: #666;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: flex-start;
      }
    }
    
    .events-column {
      flex: 1;
      position: relative;
      
      .hour-slot {
        height: 80px;
        border-bottom: 1px solid #f0f0f0;
        
        &.current-hour {
          background-color: rgba(25, 118, 210, 0.05);
        }
      }
      
      .event-item {
        position: absolute;
        background-color: white;
        border: 1px solid #e0e0e0;
        border-left: 4px solid var(--q-primary);
        border-radius: 4px;
        padding: 12px;
        cursor: pointer;
        z-index: 1;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: box-shadow 0.2s;
        
        &:hover {
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        .event-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          
          .event-time {
            font-size: 12px;
            color: #666;
            font-weight: 500;
          }
          
          .event-status {
            font-size: 10px;
          }
        }
        
        .event-title {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 8px;
          color: #333;
        }
        
        .event-patient,
        .event-location {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #666;
          margin-bottom: 4px;
        }
        
        .event-description {
          font-size: 14px;
          color: #666;
          margin-top: 8px;
          line-height: 1.4;
        }
        
        // Event type colors for left border
        &.event-primary { border-left-color: var(--q-primary); }
        &.event-secondary { border-left-color: var(--q-secondary); }
        &.event-accent { border-left-color: var(--q-accent); }
        &.event-info { border-left-color: var(--q-info); }
        &.event-warning { border-left-color: var(--q-warning); }
        &.event-positive { border-left-color: var(--q-positive); }
        &.event-negative { border-left-color: var(--q-negative); }
        &.event-grey { border-left-color: #9e9e9e; }
      }
      
      .current-time-indicator {
        position: absolute;
        left: 0;
        right: 0;
        z-index: 2;
        
        .time-line {
          height: 2px;
          background-color: #f44336;
          position: relative;
        }
        
        .time-dot {
          position: absolute;
          left: -4px;
          top: -3px;
          width: 8px;
          height: 8px;
          background-color: #f44336;
          border-radius: 50%;
        }
      }
    }
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .day-view {
    .day-grid {
      .time-column {
        width: 60px;
        
        .time-slot {
          height: 60px;
          font-size: 12px;
        }
      }
      
      .events-column {
        .hour-slot {
          height: 60px;
        }
        
        .event-item {
          padding: 8px;
          
          .event-title {
            font-size: 14px;
          }
          
          .event-patient,
          .event-location,
          .event-description {
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
