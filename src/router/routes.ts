import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      { path: '', component: () => import('pages/IndexPage.vue') },
      { path: 'calendar', component: () => import('pages/CalendarPage.vue') },
      { path: 'event-types', component: () => import('pages/EventTypesPage.vue') },
      { path: 'patients', component: () => import('pages/PatientListPage.vue') },
      { path: 'patients/create', component: () => import('pages/PatientFormPage.vue') },
      { path: 'patients/:id/edit', component: () => import('pages/PatientFormPage.vue') },
    ],
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

export default routes;
