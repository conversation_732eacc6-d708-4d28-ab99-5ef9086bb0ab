import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Patient, PatientFormData, PatientFilters } from 'src/models/patient';
import { PatientStatus, Gender } from 'src/models/patient';

export const usePatientStore = defineStore('patient', () => {
  // State
  const patients = ref<Patient[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Mock data for development
  const mockPatients: Patient[] = [
    {
      id: '1',
      name: '<PERSON>',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      phone: '(11) 99999-9999',
      whatsapp: '(11) 99999-9999',
      birthDate: '1985-05-15',
      address: 'Rua das Flores, 123',
      city: 'São Paulo',
      state: 'SP',
      zipCode: '01234-567',
      cpf: '123.456.789-00',
      rg: '12.345.678-9',
      gender: Gender.MALE,
      status: PatientStatus.ACTIVE,
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z',
    },
    {
      id: '2',
      name: '<PERSON>',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      phone: '(11) 88888-8888',
      whatsapp: '(11) 88888-8888',
      birthDate: '1990-08-22',
      address: 'Av. Paulista, 456',
      city: 'São Paulo',
      state: 'SP',
      zipCode: '01310-100',
      cpf: '987.654.321-00',
      rg: '98.765.432-1',
      gender: Gender.FEMALE,
      status: PatientStatus.ACTIVE,
      createdAt: '2024-01-16T14:30:00Z',
      updatedAt: '2024-01-16T14:30:00Z',
    },
    {
      id: '3',
      name: 'Pedro',
      lastName: 'Oliveira',
      email: '<EMAIL>',
      phone: '(21) 77777-7777',
      whatsapp: '(21) 77777-7777',
      birthDate: '1978-12-03',
      address: 'Rua Copacabana, 789',
      city: 'Rio de Janeiro',
      state: 'RJ',
      zipCode: '22070-001',
      cpf: '456.789.123-00',
      rg: '45.678.912-3',
      gender: Gender.MALE,
      status: PatientStatus.INACTIVE,
      createdAt: '2024-01-17T09:15:00Z',
      updatedAt: '2024-01-20T16:45:00Z',
    },
  ];

  // Getters
  const activePatients = computed(() =>
    patients.value.filter((patient) => patient.status === PatientStatus.ACTIVE),
  );

  const inactivePatients = computed(() =>
    patients.value.filter((patient) => patient.status === PatientStatus.INACTIVE),
  );

  const totalPatients = computed(() => patients.value.length);

  // Actions
  const loadPatients = async (filters?: PatientFilters) => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      let filteredPatients = [...mockPatients];

      if (filters) {
        if (filters.search) {
          const searchTerm = filters.search.toLowerCase();
          filteredPatients = filteredPatients.filter(
            (patient) =>
              patient.name.toLowerCase().includes(searchTerm) ||
              patient.lastName.toLowerCase().includes(searchTerm) ||
              patient.email?.toLowerCase().includes(searchTerm) ||
              patient.phone.includes(searchTerm),
          );
        }

        if (filters.status) {
          filteredPatients = filteredPatients.filter(
            (patient) => patient.status === filters.status,
          );
        }

        if (filters.gender) {
          filteredPatients = filteredPatients.filter(
            (patient) => patient.gender === filters.gender,
          );
        }

        if (filters.state) {
          filteredPatients = filteredPatients.filter((patient) => patient.state === filters.state);
        }
      }

      patients.value = filteredPatients;
    } catch (err) {
      error.value = 'Erro ao carregar pacientes';
      console.error('Error loading patients:', err);
    } finally {
      loading.value = false;
    }
  };

  const getPatientById = (id: string): Patient | undefined => {
    return patients.value.find((patient) => patient.id === id);
  };

  const createPatient = async (patientData: PatientFormData): Promise<Patient> => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      const newPatient: Patient = {
        id: Date.now().toString(),
        ...patientData,
        status: PatientStatus.ACTIVE,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      patients.value.push(newPatient);
      mockPatients.push(newPatient); // Update mock data

      return newPatient;
    } catch (err) {
      error.value = 'Erro ao criar paciente';
      console.error('Error creating patient:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updatePatient = async (id: string, patientData: Partial<Patient>): Promise<Patient> => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      const patientIndex = patients.value.findIndex((patient) => patient.id === id);
      if (patientIndex === -1) {
        throw new Error('Paciente não encontrado');
      }

      const updatedPatient: Patient = {
        ...patients.value[patientIndex],
        ...(patientData as Patient),
        updatedAt: new Date().toISOString(),
      };

      patients.value[patientIndex] = updatedPatient;

      // Update mock data
      const mockIndex = mockPatients.findIndex((patient) => patient.id === id);
      if (mockIndex !== -1) {
        mockPatients[mockIndex] = updatedPatient;
      }

      return updatedPatient;
    } catch (err) {
      error.value = 'Erro ao atualizar paciente';
      console.error('Error updating patient:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const deletePatient = async (id: string): Promise<void> => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      const patientIndex = patients.value.findIndex((patient) => patient.id === id);
      if (patientIndex === -1) {
        throw new Error('Paciente não encontrado');
      }

      patients.value.splice(patientIndex, 1);

      // Update mock data
      const mockIndex = mockPatients.findIndex((patient) => patient.id === id);
      if (mockIndex !== -1) {
        mockPatients.splice(mockIndex, 1);
      }
    } catch (err) {
      error.value = 'Erro ao excluir paciente';
      console.error('Error deleting patient:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const togglePatientStatus = async (id: string): Promise<Patient> => {
    loading.value = true;
    error.value = null;

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      const patientIndex = patients.value.findIndex((patient) => patient.id === id);
      if (patientIndex === -1) {
        throw new Error('Paciente não encontrado');
      }

      const currentPatient = patients.value[patientIndex];
      if (!currentPatient) {
        throw new Error('Paciente não encontrado');
      }
      const currentStatus = currentPatient.status;
      const newStatus =
        currentStatus === PatientStatus.ACTIVE ? PatientStatus.INACTIVE : PatientStatus.ACTIVE;

      const updatedPatient: Patient = {
        ...currentPatient,
        status: newStatus,
        updatedAt: new Date().toISOString(),
      };

      patients.value[patientIndex] = updatedPatient;

      // Update mock data
      const mockIndex = mockPatients.findIndex((patient) => patient.id === id);
      if (mockIndex !== -1) {
        mockPatients[mockIndex] = updatedPatient;
      }

      return updatedPatient;
    } catch (err) {
      error.value = 'Erro ao alterar status do paciente';
      console.error('Error toggling patient status:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // Initialize with mock data
  patients.value = [...mockPatients];

  return {
    // State
    patients,
    loading,
    error,

    // Getters
    activePatients,
    inactivePatients,
    totalPatients,

    // Actions
    loadPatients,
    getPatientById,
    createPatient,
    updatePatient,
    deletePatient,
    togglePatientStatus,
  };
});
