export interface CustomEventType {
  id: string;
  name: string;
  description?: string;
  icon: string;
  color: string;
  isActive: boolean;
  isDefault: boolean; // For built-in types that cannot be deleted
  createdAt: string;
  updatedAt: string;
}

export interface EventTypeFormData {
  name: string;
  description?: string;
  icon: string;
  color: string;
  isActive: boolean;
}

export interface EventTypeFilters {
  search?: string;
  isActive?: boolean;
}

// Available icons for event types
export const AVAILABLE_ICONS = [
  { value: 'medical_services', label: 'Serviços Médicos' },
  { value: 'assignment', label: 'Atribuição' },
  { value: 'healing', label: 'Cura' },
  { value: 'science', label: 'Ciência' },
  { value: 'local_hospital', label: 'Hospital' },
  { value: 'groups', label: 'Grupos' },
  { value: 'event', label: 'Evento' },
  { value: 'calendar_today', label: 'Calendário' },
  { value: 'schedule', label: 'Hor<PERSON><PERSON>' },
  { value: 'person', label: 'Pessoa' },
  { value: 'business', label: 'Negó<PERSON>' },
  { value: 'school', label: 'Escola' },
  { value: 'work', label: 'Trabal<PERSON>' },
  { value: 'home', label: 'Casa' },
  { value: 'phone', label: 'Telefone' },
  { value: 'email', label: 'Email' },
  { value: 'location_on', label: 'Localização' },
  { value: 'fitness_center', label: 'Academia' },
  { value: 'restaurant', label: 'Restaurante' },
  { value: 'shopping_cart', label: 'Compras' },
  { value: 'directions_car', label: 'Carro' },
  { value: 'flight', label: 'Voo' },
  { value: 'hotel', label: 'Hotel' },
  { value: 'camera_alt', label: 'Câmera' },
  { value: 'music_note', label: 'Música' },
  { value: 'sports_soccer', label: 'Esportes' },
  { value: 'pets', label: 'Animais' },
  { value: 'cake', label: 'Bolo' },
  { value: 'celebration', label: 'Celebração' },
  { value: 'favorite', label: 'Favorito' },
  { value: 'star', label: 'Estrela' },
  { value: 'lightbulb', label: 'Lâmpada' },
  { value: 'build', label: 'Construir' },
  { value: 'settings', label: 'Configurações' },
  { value: 'help', label: 'Ajuda' },
  { value: 'info', label: 'Informação' },
  { value: 'warning', label: 'Aviso' },
  { value: 'error', label: 'Erro' },
  { value: 'check_circle', label: 'Verificado' },
  { value: 'cancel', label: 'Cancelar' },
  { value: 'add_circle', label: 'Adicionar' },
  { value: 'remove_circle', label: 'Remover' },
];

// Available colors for event types
export const AVAILABLE_COLORS = [
  { value: 'primary', label: 'Azul Principal', hex: '#1976d2' },
  { value: 'secondary', label: 'Roxo Secundário', hex: '#26a69a' },
  { value: 'accent', label: 'Rosa Destaque', hex: '#9c27b0' },
  { value: 'positive', label: 'Verde Positivo', hex: '#21ba45' },
  { value: 'negative', label: 'Vermelho Negativo', hex: '#c10015' },
  { value: 'info', label: 'Azul Informação', hex: '#31ccec' },
  { value: 'warning', label: 'Laranja Aviso', hex: '#f2c037' },
  { value: 'orange', label: 'Laranja', hex: '#ff9800' },
  { value: 'deep-orange', label: 'Laranja Escuro', hex: '#ff5722' },
  { value: 'red', label: 'Vermelho', hex: '#f44336' },
  { value: 'pink', label: 'Rosa', hex: '#e91e63' },
  { value: 'purple', label: 'Roxo', hex: '#9c27b0' },
  { value: 'deep-purple', label: 'Roxo Escuro', hex: '#673ab7' },
  { value: 'indigo', label: 'Índigo', hex: '#3f51b5' },
  { value: 'blue', label: 'Azul', hex: '#2196f3' },
  { value: 'light-blue', label: 'Azul Claro', hex: '#03a9f4' },
  { value: 'cyan', label: 'Ciano', hex: '#00bcd4' },
  { value: 'teal', label: 'Verde-azulado', hex: '#009688' },
  { value: 'green', label: 'Verde', hex: '#4caf50' },
  { value: 'light-green', label: 'Verde Claro', hex: '#8bc34a' },
  { value: 'lime', label: 'Lima', hex: '#cddc39' },
  { value: 'yellow', label: 'Amarelo', hex: '#ffeb3b' },
  { value: 'amber', label: 'Âmbar', hex: '#ffc107' },
  { value: 'brown', label: 'Marrom', hex: '#795548' },
  { value: 'grey', label: 'Cinza', hex: '#9e9e9e' },
  { value: 'blue-grey', label: 'Azul Acinzentado', hex: '#607d8b' },
];

// Helper functions
export const getIconOption = (icon: string) => {
  return AVAILABLE_ICONS.find(option => option.value === icon);
};

export const getColorOption = (color: string) => {
  return AVAILABLE_COLORS.find(option => option.value === color);
};

export const getColorHex = (color: string): string => {
  const colorOption = getColorOption(color);
  return colorOption?.hex || '#1976d2';
};

// Default event types (built-in)
export const DEFAULT_EVENT_TYPES: Omit<CustomEventType, 'id' | 'createdAt' | 'updatedAt'>[] = [
  {
    name: 'Consulta',
    description: 'Consulta médica com paciente',
    icon: 'medical_services',
    color: 'primary',
    isActive: true,
    isDefault: true,
  },
  {
    name: 'Retorno',
    description: 'Consulta de retorno',
    icon: 'assignment',
    color: 'secondary',
    isActive: true,
    isDefault: true,
  },
  {
    name: 'Procedimento',
    description: 'Procedimento médico',
    icon: 'healing',
    color: 'accent',
    isActive: true,
    isDefault: true,
  },
  {
    name: 'Exame',
    description: 'Exame médico ou laboratorial',
    icon: 'science',
    color: 'info',
    isActive: true,
    isDefault: true,
  },
  {
    name: 'Cirurgia',
    description: 'Procedimento cirúrgico',
    icon: 'local_hospital',
    color: 'warning',
    isActive: true,
    isDefault: true,
  },
  {
    name: 'Reunião',
    description: 'Reunião de equipe ou administrativa',
    icon: 'groups',
    color: 'positive',
    isActive: true,
    isDefault: true,
  },
  {
    name: 'Outro',
    description: 'Outros tipos de eventos',
    icon: 'event',
    color: 'grey',
    isActive: true,
    isDefault: true,
  },
];

// Validation rules
export const validateEventTypeName = (name: string): string | true => {
  if (!name || name.trim().length === 0) {
    return 'Nome é obrigatório';
  }
  if (name.trim().length < 2) {
    return 'Nome deve ter pelo menos 2 caracteres';
  }
  if (name.trim().length > 50) {
    return 'Nome deve ter no máximo 50 caracteres';
  }
  return true;
};

export const validateEventTypeDescription = (description?: string): string | true => {
  if (description && description.trim().length > 200) {
    return 'Descrição deve ter no máximo 200 caracteres';
  }
  return true;
};
